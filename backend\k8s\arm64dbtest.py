import oracledb
dsn = """
(description=
    (retry_count=20)
    (retry_delay=3)
    (address=(protocol=tcps)(port=1521)(host=adb.eu-frankfurt-1.oraclecloud.com))
    (connect_data=(service_name=g23416de76093b5_goodnews_low.adb.oraclecloud.com))
    (security=(ssl_server_dn_match=yes))
)
"""
username = "DEVXPS15"
password = "RZt72xEvCYczkrd"
try:
    # Connect to the Oracle database
    connection = oracledb.connect(user=username, password=password, dsn=dsn)
    print("Connection successful!")
    # Execute a simple query to test the connection
    cursor = connection.cursor()
    cursor.execute("SELECT sysdate FROM dual")
    result = cursor.fetchone()
    print("Database time:", result[0])

except oracledb.DatabaseError as e:
    print("Database connection failed:", e)
finally:
    if 'connection' in locals() and connection:
        connection.close()