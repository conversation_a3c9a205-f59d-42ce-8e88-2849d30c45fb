{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "news_aggregator",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/etl/news_aggregator.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "development",
            }
        },
        {
            "name": "Python Debugger: FastAPI",
            "type": "debugpy",
            "request": "launch",
            "module": "uvicorn",
            "args": [
                "backend.app.main:app",
                "--reload",
                "--host", "0.0.0.0",
                "--workers=1",
                "--reload-dir=backend/app",
                "--log-level=trace"
            ],
            "envFile": "${workspaceFolder}/.env",
            "cwd": "${workspaceFolder}", 
            "jinja": true,
            "justMyCode": false
        },
        {
            "name": "positive_news_app",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "positive_news_app (profile mode)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "positive_news_app (release mode)",
            "cwd": "frontend\\positive_news_app",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}
